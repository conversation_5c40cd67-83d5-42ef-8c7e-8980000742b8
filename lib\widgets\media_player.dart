import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:wicker/widgets/aspect_ratio_container.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class MediaPlayer extends StatefulWidget {
  final Map<String, dynamic> mediaData;
  final String baseUrl;

  const MediaPlayer({
    super.key,
    required this.mediaData,
    required this.baseUrl,
  });

  @override
  State<MediaPlayer> createState() => _MediaPlayerState();
}

class _MediaPlayerState extends State<MediaPlayer> {
  VideoPlayerController? _videoController;

  @override
  void initState() {
    super.initState();
    if (widget.mediaData['type'] == 'video') {
      final videoPath = widget.mediaData['path']?.replaceAll('\\', '/') ?? '';
      final videoUrl = '${widget.baseUrl}/$videoPath';
      _videoController = VideoPlayerController.networkUrl(Uri.parse(videoUrl))
        ..initialize().then((_) {
          if (mounted) {
            setState(() {});
          }
        });
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final String type = widget.mediaData['type'] ?? 'unknown';
    final String path = widget.mediaData['path']?.replaceAll('\\', '/') ?? '';
    final String mediaUrl = '${widget.baseUrl}/$path';
    final double aspectRatio = (widget.mediaData['aspect_ratio'] as num?)?.toDouble() ?? 16 / 9;

    switch (type) {
      case 'image':
        return AspectRatioContainer(imageUrl: mediaUrl, aspectRatio: aspectRatio);
      case 'video':
        return _videoController != null && _videoController!.value.isInitialized
            ? AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    VideoPlayer(_videoController!),
                    IconButton(
                      icon: Icon(
                        _videoController!.value.isPlaying ? Icons.pause_circle_filled : Icons.play_circle_filled,
                        color: Colors.white,
                        size: 60,
                      ),
                      onPressed: () {
                        setState(() {
                          _videoController!.value.isPlaying
                              ? _videoController!.pause()
                              : _videoController!.play();
                        });
                      },
                    )
                  ],
                ),
              )
            : Container(
                color: Colors.black,
                child: const Center(child: CircularProgressIndicator()),
              );
      case 'audio':
        return Container(
          height: 300,
          color: Colors.grey.shade200,
          child: const Center(child: Icon(EvaIcons.music, size: 80, color: Colors.black54)),
        );
      default:
        return Container(height: 300, color: Colors.grey, child: const Center(child: Text('Unsupported Media')));
    }
  }
}