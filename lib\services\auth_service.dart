import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AuthService {
  // Use ******** for Android Emulator, and localhost for iOS simulator/web
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://*************:5000"
      : "http://127.0.0.1:5000";

  final _storage = const FlutterSecureStorage();
  static const _accessTokenKey = 'access_token';
  static const _refreshTokenKey = 'refresh_token';

  // Add timeout configuration
  static const Duration _requestTimeout = Duration(seconds: 15);

  /// Saves both access and refresh tokens securely
  Future<void> _saveTokens({
    required String accessToken,
    String? refreshToken,
  }) async {
    await _storage.write(key: _accessTokenKey, value: accessToken);
    if (refreshToken != null) {
      await _storage.write(key: _refreshTokenKey, value: refreshToken);
    }
  }

  /// Gets the access token
  Future<String?> getAccessToken() async {
    return await _storage.read(key: _accessTokenKey);
  }

  /// Gets the refresh token
  Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }

  /// Gets a valid access token, refreshing if necessary
  Future<String?> getToken() async {
    String? accessToken = await getAccessToken();

    if (accessToken == null) {
      return null;
    }

    // Check if token is expired (you might want to implement JWT parsing here)
    // For now, we'll try to use it and refresh if we get 401 errors
    return accessToken;
  }

  /// Handles user signup by calling the backend API.
  /// Returns a success message or throws an error.
  Future<String> signup({
    required String username,
    required String email,
    required String password,
  }) async {
    try {
      print('Attempting signup to: $_baseUrl/api/auth/register');
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/register'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(<String, String>{
          'username': username,
          'email': email,
          'password': password,
        }),
      ).timeout(_requestTimeout);

      print('Signup response status: ${response.statusCode}');
      print('Signup response body: ${response.body}');

      final responseBody = jsonDecode(response.body);

      if (response.statusCode == 201) {
        return responseBody['msg']; // "User created successfully"
      } else {
        // Throws an error with the message from the backend (e.g., "Email already exists")
        throw Exception(responseBody['msg']);
      }
    } catch (e) {
      print('Signup error: $e');
      // If it's already an Exception we threw, re-throw it
      if (e is Exception) {
        rethrow;
      }
      // Otherwise, wrap network/parsing errors
      throw Exception('Failed to connect to server: $e');
    }
  }

  /// Handles user login by calling the backend API.
  /// Saves the token on success.
  /// Returns true on success or throws an error.
  Future<bool> login({required String email, required String password}) async {
    try {
      print('Attempting login to: $_baseUrl/api/auth/login');
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/login'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(<String, String>{
          'email': email,
          'password': password,
        }),
      ).timeout(_requestTimeout);

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        await _saveTokens(
          accessToken: responseBody['access_token'],
          refreshToken:
              responseBody['refresh_token'], // Save refresh token if provided
        );
        return true;
      } else {
        final responseBody = jsonDecode(response.body);
        // Throws an error with the message from the backend (e.g., "Bad email or password")
        throw Exception(responseBody['msg']);
      }
    } catch (e) {
      print('Login error: $e');
      // If it's already an Exception we threw, re-throw it
      if (e is Exception) {
        rethrow;
      }
      // Otherwise, wrap network/parsing errors
      throw Exception('Failed to connect to server: $e');
    }
  }

  // THE CHANGE: Now deletes both tokens
  Future<void> logout() async {
    await _storage.delete(key: _accessTokenKey);
    await _storage.delete(key: _refreshTokenKey);
  }

  /// Refreshes the access token using the refresh token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        // If there's no refresh token, we can't refresh. Force logout.
        await logout();
        return false;
      }

      print('Attempting to refresh token at: $_baseUrl/api/auth/refresh');
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/refresh'),
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $refreshToken',
        },
      );

      print('Refresh token response status: ${response.statusCode}');
      print('Refresh token response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        // Save the new access token (and refresh token if provided)
        await _saveTokens(
          accessToken: responseBody['access_token'],
          refreshToken:
              responseBody['refresh_token'], // Some APIs provide new refresh token
        );
        return true;
      } else {
        // If refresh fails, the refresh token is likely expired or invalid. Force logout.
        print('Token refresh failed, logging out user');
        await logout();
        return false;
      }
    } catch (e) {
      print('Token refresh error: $e');
      await logout();
      return false;
    }
  }

  /// Makes an authenticated HTTP request with automatic token refresh
  Future<http.Response> authenticatedRequest({
    required String method,
    required String endpoint,
    Map<String, String>? headers,
    Object? body,
  }) async {
    String? token = await getToken();

    if (token == null) {
      throw Exception('Authentication required. Please log in.');
    }

    final requestHeaders = {
      'Content-Type': 'application/json; charset=UTF-8',
      'Authorization': 'Bearer $token',
      ...?headers,
    };

    http.Response response;

    // Make the initial request
    response = await _makeHttpRequest(method, endpoint, requestHeaders, body);

    // If we get a 401 (Unauthorized), try to refresh the token and retry once
    if (response.statusCode == 401) {
      print('Received 401, attempting to refresh token...');
      final refreshed = await refreshToken();

      if (refreshed) {
        // Retry the request with the new token
        token = await getToken();
        if (token != null) {
          requestHeaders['Authorization'] = 'Bearer $token';
          response = await _makeHttpRequest(
            method,
            endpoint,
            requestHeaders,
            body,
          );
        }
      }
    }

    return response;
  }

  /// Helper method to make HTTP requests
  Future<http.Response> _makeHttpRequest(
    String method,
    String endpoint,
    Map<String, String> headers,
    Object? body,
  ) async {
    switch (method.toUpperCase()) {
      case 'GET':
        return await http.get(
          Uri.parse('$_baseUrl$endpoint'),
          headers: headers,
        );
      case 'POST':
        return await http.post(
          Uri.parse('$_baseUrl$endpoint'),
          headers: headers,
          body: body,
        );
      case 'PUT':
        return await http.put(
          Uri.parse('$_baseUrl$endpoint'),
          headers: headers,
          body: body,
        );
      case 'DELETE':
        return await http.delete(
          Uri.parse('$_baseUrl$endpoint'),
          headers: headers,
        );
      default:
        throw Exception('Unsupported HTTP method: $method');
    }
  }
}
