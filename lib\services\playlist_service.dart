import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:wicker/services/places_service.dart'; // We'll use the WickerHttpClient

class PlaylistService {
  final WickerHttpClient _client = WickerHttpClient();
  // Base URL can be defined here or inherited
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://192.168.8.107:5000"
      : "http://127.0.0.1:5000";

  Future<String> createPlaylist({
    required String name,
    required bool isPrivate,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/api/playlists/create'),
        body: jsonEncode({'name': name, 'is_private': isPrivate}),
      );

      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return responseBody['msg'];
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to create playlist');
      }
    } catch (e) {
      rethrow;
    }
  }

  // NEW: Method to fetch the user's playlists
  Future<List<Map<String, dynamic>>> getMyPlaylists() async {
    try {
      final response = await _client.get(Uri.parse('$_baseUrl/api/playlists/'));
      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load playlists');
      }
    } catch (e) {
      rethrow;
    }
  }

  // NEW: Method to fetch a playlist's details
  Future<Map<String, dynamic>> getPlaylistDetails(String playlistId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/api/playlists/$playlistId'),
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load playlist details');
      }
    } catch (e) {
      rethrow;
    }
  }
}
